package com.snct.common.core.domain;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.snct.common.constant.UserConstants;
import com.snct.common.core.domain.entity.SysDept;
import com.snct.common.core.domain.entity.SysMenu;
import com.snct.common.utils.StringUtils;

/**
 * Treeselect树结构实体类
 * 
 * <AUTHOR>
 */
public class TreeSelect implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;

    /** 节点禁用 */
    private boolean disabled = false;

    private Integer level  = 0;

    private Integer online  = 0;   //默认  1在线  2 离线

    private Integer warning  = 0;   //默认  1预警  2 正常

    /** 节点禁用 */
    private Long parentId  = null;  //非子级  1子级

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect> children;

    public TreeSelect()
    {

    }

    public TreeSelect(SysDept dept)
    {
        this.id = dept.getDeptId();
        this.label = dept.getDeptName();
        this.parentId = dept.getParentId();
        this.disabled = StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus());
        if(dept.getDeptCode() == null || "".equals(dept.getDeptCode())){
            //1船  0默认
            level = 1;
            //1在线，2离线
            online = 1;
            //1预警，2正常   //检查是否有预警
            warning = 1;
        }
        this.children = dept.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(SysMenu menu)
    {
        this.id = menu.getMenuId();
        this.label = menu.getMenuName();
        this.parentId = menu.getParentId();
        this.children = menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public Integer getWarning()
    {
        return warning;
    }

    public void setWarning(Integer warning)
    {
        this.warning = warning;
    }

    public Long getParentId()
    {
        return parentId;
    }

    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    public Integer getLevel()
    {
        return level;
    }

    public void setLevel(Integer level)
    {
        this.level = level;
    }

    public Integer getOnline()
    {
        return online;
    }

    public void setOnline(Integer online)
    {
        this.online = online;
    }

    public boolean isDisabled()
    {
        return disabled;
    }

    public void setDisabled(boolean disabled)
    {
        this.disabled = disabled;
    }

    public List<TreeSelect> getChildren()
    {
        return children;
    }

    public void setChildren(List<TreeSelect> children)
    {
        this.children = children;
    }

    // 判断节点是否包含关键字（忽略大小写）
    public boolean containsKeyword(String keyword) {
        return label.toLowerCase().contains(keyword.toLowerCase());
    }

}
