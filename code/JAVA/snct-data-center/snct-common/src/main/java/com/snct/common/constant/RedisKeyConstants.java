package com.snct.common.constant;

/**
 * @ClassName: RedisKeyConstants
 * @Description: Redis Key 常量
 * @author: wzewei
 * @date: 2025-08-26 14:13:47
 */
public class RedisKeyConstants {


    /**
     * 存储接收到的数据
     */
    public static String RECEIVE_COMPLETE_DATA = "RECEIVE_COMPLETE_DATA-";
    /**
     * 最大接收到的编号
     */
    public static String MAX_RECEIVE_NUM = "MAX_RECEIVE_NUM-";
    /**
     * 最大校验编号
     */
    public static String MAX_CHECK_NUM = "MAX_CHECK_NUM-";
    /**
     * 岸上丢失数据链表
     */
    public static String LOSE_DATA_LIST = "LOSE_DATA_LIST-";
    /**
     * 最大接收编号与最大校验编号的差
     */
    public static String CHECK_INTERVAL_NUM = "CHECK_INTERVAL_NUM-";


    //-----------------------------------------------------------------

    /**
     * 快照数据 临时存储
     */
    public static String SNAPSHOT_RECEIVE_TEMPORARY = "SNAPSHOT_RECEIVE_TEMPORARY-";
    /**
     * 快照数据，编号的最新获取数据时间
     */
    public static String SNAPSHOT_NEWEST_TIME = "SNAPSHOT_NEWEST_TIME-";
    /**
     * 快照数据，最新补数据时间
     */
    public static String SNAPSHOT_NEWEST_REPAIR_TIME = "SNAPSHOT_NEWEST_REPAIR_TIME-";
    /**
     * 最新接收快照的时间
     */
    public static String LATEST_PICTURE_DATE = "LATEST_PICTURE_DATE-";


    //-----------------------------------------------------------------

    /**
     * 船上同步数据
     */
    public static String SHIP_SYNC_DATA = "SHIP_SYNC_DATA-";
    /**
     * 船上同步数据
     */
    public static String SHIP_SYNC_SUCCESS = "SHIP_SYNC_SUCCESS-";

    //-----------------------------------------------------------------

    /**
     * 所有启用的船只sn号
     */
    public static String ALL_ENABLE_SHIP_SN = "ALL_ENABLE_SHIP_SN";
    /**
     * 所有启用的设备
     */
    public static String ALL_ENABLE_DEVICE_CODE = "ALL_ENABLE_DEVICE_CODE";






    public static String getKey(String key, String sn) {
        return key + sn;
    }

    public static String getKey(String key, String sn, String code) {
        return key + sn+ "_" + code;
    }

    public static String getKey(String key, String sn, Integer commandNum) {
        return key + sn + "-" + commandNum;
    }
}