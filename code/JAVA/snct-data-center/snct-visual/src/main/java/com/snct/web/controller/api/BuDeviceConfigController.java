package com.snct.web.controller.api;

import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.page.TableDataInfo;
import com.snct.common.enums.BusinessType;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.system.domain.BuDeviceConfig;
import com.snct.system.service.IBuDeviceConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备配置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@RestController
@RequestMapping("/business/deviceConfig")
public class BuDeviceConfigController extends BaseController
{
    @Autowired
    private IBuDeviceConfigService buDeviceConfigService;

//    /**
//     * 获取设备配置详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('business:deviceConfig:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(buDeviceConfigService.selectBuDeviceConfigById(id));
//    }

}
