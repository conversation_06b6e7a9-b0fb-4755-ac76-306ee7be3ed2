package com.snct.typhoon.service.impl;


import com.snct.common.core.redis.RedisCache;
import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.typhoon.domain.Typhoon;
import com.snct.typhoon.domain.vo.TyphoonInfoVo;
import com.snct.typhoon.service.SelectTyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: SelectTyServiceImpl
 * @Description: 台风数据查询服务实现类
 * @author: wzewei
 * @date: 2025-08-19 16:43:35
 */
@Service
public class SelectTyServiceImpl implements SelectTyService {

    @Autowired
    private RedisCache redisCache;

    @Override
    public List<Typhoon> selectTyphoonList(Integer yearsTime) {
        return redisCache.getCacheObject(RedisParameter.TYPHOON_LIST + yearsTime);
    }

    @Override
    public List<TyphoonInfoVo> selectTyphoonPoint(Integer tfId) {
        return redisCache.getCacheObject(RedisParameter.TYPHOON_INFO + tfId);
    }

    @Override
    public Map<String, Object> selectActiveTyphoonSummary() {
        Map<String, Object> data = redisCache.getCacheObject(RedisParameter.TYPHOON_ACTIVE_SUMMARY);
        return data != null ? data : new HashMap<>();
    }
}
