package com.snct;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@EnableDiscoveryClient
@EnableScheduling
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class SnctVisualApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(SnctVisualApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  SNCT系统启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    }
}
