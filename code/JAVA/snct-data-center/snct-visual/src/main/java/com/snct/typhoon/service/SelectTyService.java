package com.snct.typhoon.service;


import com.snct.typhoon.domain.Typhoon;
import com.snct.typhoon.domain.vo.TyphoonInfoVo;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: SelectTyService
 * @Description: 台风查询服务接口
 * @author: wze<PERSON>
 * @date: 2025-08-19 16:44:47
 */
public interface SelectTyService {

    List<Typhoon> selectTyphoonList(Integer yearsTime);

    List<TyphoonInfoVo> selectTyphoonPoint(Integer tfId);

    Map<String, Object> selectActiveTyphoonSummary();
}
