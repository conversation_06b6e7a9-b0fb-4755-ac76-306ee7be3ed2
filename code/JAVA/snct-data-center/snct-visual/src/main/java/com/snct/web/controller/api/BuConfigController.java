package com.snct.web.controller.api;

import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.domain.entity.SysMenu;
import com.snct.system.domain.BuCruise;
import com.snct.system.domain.BuShipWarning;
import com.snct.system.domain.Ship;
import com.snct.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 航次Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/api/config")
public class BuConfigController extends BaseController
{
    @Autowired
    private IBuCruiseService buCruiseService;
    @Autowired
    private IShipService iShipService;
    @Autowired
    private ISysMenuService menuService;
    /**
     * 查询航次列表
     */
    @PreAuthorize("@ss.hasPermi('screen:base:Info')")
    @GetMapping("/routeConfigList")
    public AjaxResult routeConfigList()
    {
        SysMenu menu = new SysMenu();
        menu.setPerms("screen");
        List<SysMenu> mlsit = menuService.selectScreenMenuList(menu,getUserId());
        List relist = new ArrayList();
        for(SysMenu sysMenu : mlsit){
            Map map = new HashMap();
            map.put("configId",sysMenu.getMenuId());
            map.put("configName",sysMenu.getMenuName());
            map.put("configKey",sysMenu.getPath());
            relist.add(map);
        }
        return AjaxResult.success(relist);
    }

}
