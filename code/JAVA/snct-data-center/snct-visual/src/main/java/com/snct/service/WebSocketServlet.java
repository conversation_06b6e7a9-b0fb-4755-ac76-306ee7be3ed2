package com.snct.service;

import com.alibaba.fastjson.JSONObject;
import com.snct.common.core.domain.entity.SysDept;
import com.snct.common.core.domain.model.LoginUser;
import com.snct.common.utils.spring.SpringUtils;
import com.snct.framework.web.service.TokenService;
import com.snct.system.domain.Ship;
import com.snct.system.domain.dto.ShipSimpleDto;
import com.snct.system.service.IShipService;
import com.snct.system.service.ISysDeptService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * @description:
 * @author: snct
 **/
@ServerEndpoint(value = "/websocket/{token}")
@Component
public class WebSocketServlet {

    private Logger logger = LoggerFactory.getLogger(WebSocketServlet.class);

    private ScheduledExecutorService scheduled = null;
    private ScheduledFuture<?> future = null;
    /**
     * 存放session以及对应的页面
     */
    public static ConcurrentHashMap<Session, Map<String, String>> hashMap_Session = new ConcurrentHashMap<Session, Map<String, String>>();
    /**
     * 判断是否启动线程
     */
    private static boolean isRun = true;
    private Session session;
    private CronSendData cron;

    /**
     * @ClassName: onOpen
     * @Description: 开启连接的操作
     */
    @OnOpen
    public void onOpen(@PathParam(value = "token") String token, Session session) {
        this.session = session;
        logger.info("WebSocket接收[连接]请求。。。。token:{}" ,token.substring(0,4));
        Map<String, String> map = new HashMap();
        map.put("code", "0000");
        //map.put("token", token);  //登录凭证
        map.put("time", ((int)(System.currentTimeMillis()/1000))+"" );  //连接建立时间

        TokenService tokenService = SpringUtils.getBean(TokenService.class);
        LoginUser user = tokenService.getLoginUUID(token);
            //map.put("token", "invalid_token");
            try{
                if( user==null  ){
                    map.put("code", "0009");
                    session.getBasicRemote().sendText(JSONObject.toJSONString(map));
                    session.close();
                    return;
                }
                session.getBasicRemote().sendText(JSONObject.toJSONString(map));
            }catch(Exception e){}
            logger.info("WebSocket连接 [token验证不通过] ！！！！！:{}" ,token);
        //保存会话
        hashMap_Session.put(session, map);
    }

    /**
     * @ClassName: onClose
     * @Description: 连接关闭的操作
     */
    @OnClose
    public void onClose() {
        if(hashMap_Session.get(session)!=null){
            logger.info("WebSocket结束请求。。。session:{}", hashMap_Session.get(session).get("token") );
        }
        hashMap_Session.remove(this.session);
        if (hashMap_Session.size() <= 0) {
            logger.info("[无有效会话],停止发送数据。。。。。。");
            isRun = true;
            if (scheduled != null && !scheduled.isShutdown()) {
                scheduled.shutdown();
                scheduled = null;
            }
        }
    }

    /**
     * 数据包代码说明：
     * 0A01 入驻企业列表
     * 0A02 入驻企业/船只数量汇总
     * 0A03 天气信息/预警信息（暂定）
     * 0A04 地图数据-企业名称与坐标列表
     * 0B01 船基本信息
     * 0B02 姿态信息
     * 0B03 气象信息信息
     * 0B04 天气信息/预警信息（暂定）
     * 0B05 企业总接入船只数量
     * 0B06 地图数据-船只名称与坐标列表
     * 0C01 视频监控实时播放地址列表
     * 0C02 视频监控历史记录列表
     * 0D01 船基本信息  （与  0B01可以通用）
     * 0D02 历史轨迹节点信息
     * 0E01 PDU信息
     * 0E02 卫星猫信息
     * 0E03 功放信息
     * @param msgs 客户端发送过来的消息  格式：type66#deptId#sn#codes） type66固定表示请求数据包   codes可以多个，逗号隔开
     */
    @OnMessage
    public void onMessage(String msgs, Session session) {
        logger.info("WebSocket接收消息。。。msgs:{}" ,msgs);
        try{
            Map<String, String> map =  hashMap_Session.get(session);
            if(map==null){
                return;
            }
            if((msgs+"").indexOf("type66")!=-1 ){

                ISysDeptService deptService = SpringUtils.getBean(ISysDeptService.class);
                IShipService shipService = SpringUtils.getBean(IShipService.class);

                String[] msg = msgs.split("#");
                String deptId = msg[1]+"";        //请求的数据归属企业id
                try{Long.valueOf(deptId);}catch (Exception e){e.printStackTrace();}   //数据格式检查
                String sn = msg[2]+"";                                                //船代码，一级首页没有具体的船用0表示
                String codes = msg[3]+"";                                             //数据包代码，请查看对照表

                if (StringUtils.isBlank(codes)) {
                    //请求的数据包代码空直接返回
                    return;
                }

                SysDept sysDept = StringUtils.isBlank(deptId)? null : deptService.selectDeptById(Long.valueOf(deptId));
                if(sysDept==null){
                    //获取默认入驻企业 并 置空sn
                    sn = "";
                    deptId = defaultDept(deptService);
                    if(StringUtils.isBlank(deptId)){
                        //未取到默认值返回
                        return;
                    }
                }
                Ship ship = StringUtils.isBlank(sn) ? null : shipService.selectShipByShipSn(sn);
                if(ship==null){
                    //获取默认船只
                    List<ShipSimpleDto> list = shipService.selectSimpleShipListByDeptId(Long.parseLong(deptId));
                    if(list.size()>0) {
                        //默认第一个
                        sn = list.get(0).getSn();
                    }
                }

                map.put("sn", sn );
                map.put("deptId", deptId);
                map.put("codes", codes);
                hashMap_Session.put(session, map);
                //开启线程
                if (isRun) {
                    scheduled = Executors.newScheduledThreadPool(1);
                    logger.info("[启动线程]送数据。。。。。");
                    isRun = false;
                    cron = new CronSendData(hashMap_Session);
                    scheduled.scheduleAtFixedRate(cron, 0, 1, TimeUnit.SECONDS);
                }
            }else{
                logger.info("########## 无效消息数据！#############");
            }

        }catch (Exception e){
            //e.printStackTrace();
            logger.error("########## 数据格式错误！#############");
        }

    }

    /**
     * @ClassName: OnError
     * @Description: 出错的操作
     */
    @OnError
    public void onError(Session session, Throwable error) {
        error.printStackTrace();
    }

    private String defaultDept(ISysDeptService deptService) {
        List<SysDept> dlist = deptService.selectDeptByPid(0l);
        if(dlist.size()==1) {
            SysDept dept = dlist.get(0);
            List<SysDept> list = deptService.selectDeptByPid(dept.getDeptId());
            if(list.size()>0) {
                //默认第一个
                return list.get(0).getDeptId()+"";
            }
        }
        return "";
    }

}