package com.snct.typhoon.domain.vo;

import com.snct.common.utils.bean.BeanUtils;
import com.snct.typhoon.domain.TyphoonLand;

/**
 * @ClassName: TyphoonLandVo
 * @Description: 台风登陆信息视图类
 * @author: wzewei
 * @date: 2025-08-19 16:41:38
 */
public class TyphoonLandVo {

    /**
     * 消息
     */
    private String info;

    /**
     * 登陆地址
     */
    private String landaddress;

    /**
     * 登陆时间
     */
    private String landtime;

    private Long landTimeStamp;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lng;

    /**
     * 强度
     */
    private String strong;


    /**
     * 包装类转对象
     *
     * @param TyphoonLandVo
     * @return
     */
    public static TyphoonLand voToObj(TyphoonLandVo TyphoonLandVo) {
        if (TyphoonLandVo == null) {
            return null;
        }
        TyphoonLand TyphoonLand = new TyphoonLand();
        BeanUtils.copyBeanProp(TyphoonLand, TyphoonLandVo);
        return TyphoonLand;
    }

    /**
     * 对象转包装类
     *
     * @param TyphoonLand
     * @return
     */
    public static TyphoonLandVo objToVo(TyphoonLand TyphoonLand) {
        if (TyphoonLand == null) {
            return null;
        }
        TyphoonLandVo TyphoonLandVo = new TyphoonLandVo();
        BeanUtils.copyBeanProp(TyphoonLandVo, TyphoonLand);
        return TyphoonLandVo;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getLandaddress() {
        return landaddress;
    }

    public void setLandaddress(String landaddress) {
        this.landaddress = landaddress;
    }

    public String getLandtime() {
        return landtime;
    }

    public void setLandtime(String landtime) {
        this.landtime = landtime;
    }

    public Long getLandTimeStamp() {
        return landTimeStamp;
    }

    public void setLandTimeStamp(Long landTimeStamp) {
        this.landTimeStamp = landTimeStamp;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getStrong() {
        return strong;
    }

    public void setStrong(String strong) {
        this.strong = strong;
    }
}
