package com.snct.web.controller.system;

import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.domain.TreeSelect;
import com.snct.common.core.domain.entity.SysDept;
import com.snct.common.core.domain.entity.SysRole;
import com.snct.common.core.domain.entity.SysUser;
import com.snct.common.core.page.TableDataInfo;
import com.snct.common.enums.BusinessType;
import com.snct.common.utils.SecurityUtils;
import com.snct.common.utils.StringUtils;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.system.service.*;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController
{

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IShipService shipService;

    /**
     * 获取部门树列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/deptTree")
    public AjaxResult deptTree(@RequestParam(value = "name", required=false) String name)
    {
        SysDept dept = new SysDept();
        dept.setDeptId(getDeptId());
        List<TreeSelect> treeList = deptService.selectDeptTreeListScreen(dept);
        //树结构过滤算法
        List<TreeSelect> retreeList;
        if(name!=null && !"".equals(name)){
            retreeList = fuzzySearch(treeList,name);
        }else{
            retreeList = treeList;
        }
        return success(retreeList);
    }

    // 模糊查询主方法
    public static List<TreeSelect> fuzzySearch(List<TreeSelect> rootNodes, String keyword) {
        List<TreeSelect> result = new ArrayList<>();
        for (TreeSelect root : rootNodes) {
            // 克隆树结构用于过滤
            TreeSelect clonedRoot = cloneTree(root);
            // 执行过滤
            filterTree(clonedRoot, keyword);
            // 如果根节点或子节点有匹配，则加入结果
            if (clonedRoot != null) {
                result.add(clonedRoot);
            }
        }
        return result;
    }

    // 深度克隆树
    private static TreeSelect cloneTree(TreeSelect node) {
        if (node == null) return null;
        TreeSelect clone = node;//new TreeSelect(node.getId(), node.getParentId(), node.getLabel());
        List<TreeSelect> clonedChildren = new ArrayList<>();
        for (TreeSelect child : node.getChildren()) {
            clonedChildren.add(cloneTree(child));
        }
        clone.setChildren(clonedChildren);
        return clone;
    }

    // 递归过滤树（核心算法）
    private static boolean filterTree(TreeSelect node, String keyword) {
        // 1. 处理子节点（深度优先）
        List<TreeSelect> matchedChildren = new ArrayList<>();
        for (TreeSelect child : node.getChildren()) {
            if (filterTree(child, keyword)) {
                matchedChildren.add(child);
            }
        }
        // 2. 更新当前节点的子节点列表
        node.setChildren(matchedChildren);

        // 3. 检查是否保留当前节点：
        //    a. 当前节点匹配关键字
        //    b. 子节点中有匹配项（保留路径）
        return node.containsKeyword(keyword) || !matchedChildren.isEmpty();
    }


}
