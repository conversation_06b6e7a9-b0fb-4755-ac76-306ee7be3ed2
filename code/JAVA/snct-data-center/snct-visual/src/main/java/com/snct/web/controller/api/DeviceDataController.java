package com.snct.web.controller.api;

import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.utils.StringUtils;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.service.RealtimeService;
import com.snct.service.device.AttitudeService;
import com.snct.service.device.AwsService;
import com.snct.service.device.GpsService;
import com.snct.system.domain.BuCruise;
import com.snct.system.domain.BuShipWarning;
import com.snct.system.domain.Device;
import com.snct.system.domain.Ship;
import com.snct.system.service.IBuShipWarningService;
import com.snct.system.service.IShipService;
import com.snct.system.service.impl.DeviceServiceImpl;
import com.snct.system.service.impl.ShipServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 设备数据
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
//@CrossOrigin
@RestController
@RequestMapping("/api/ship")
public class DeviceDataController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(DeviceDataController.class);

    @Autowired
    private ShipServiceImpl shipService;

    @Autowired
    private DeviceServiceImpl deviceService;

    @Autowired
    private RealtimeService realtimeService;

    @Autowired
    private GpsService gpsService;

    @Autowired
    private AwsService awsService;

    @Autowired
    private AttitudeService attitudeService;

    @Autowired
    private IShipService iShipService;
    @Autowired
    private IBuShipWarningService buShipWarningService;


    /**
     * 获取船只轨迹数据
     *
     * @param shipId 船只ID
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param interval 数据间隔
     * @return 轨迹数据列表
     */
    @GetMapping("/trackList")
    public AjaxResult getTrackList(Long shipId, Long startTime, Long endTime, Integer interval) {
        try {
            // 参数验证
            if (shipId == null) {
                return AjaxResult.error("船只ID不能为空");
            }
            if (startTime == null || endTime == null) {
                return AjaxResult.error("开始时间和结束时间不能为空");
            }
            if (startTime >= endTime) {
                return AjaxResult.error("开始时间必须小于结束时间");
            }
            if (interval == null || interval <= 0) {
                return AjaxResult.error("数据间隔必须大于0");
            }

            // 查询船只信息
            Ship ship = shipService.selectShipByShipId(shipId);
            if (ship == null) {
                return AjaxResult.error("未找到指定的船只信息");
            }

            String shipSn = ship.getSn();
            if (StringUtils.isBlank(shipSn)) {
                return AjaxResult.error("船只序列号为空");
            }

            // 根据船只SN和GPS设备类型查询设备列表
            List<Device> gpsDevices = deviceService.selectSimpleDeviceListBySnAndType(shipSn,
                    Long.valueOf(DeviceTypeEnum.GPS.getValue()));

            // 检查GPS设备列表是否为空
            if (gpsDevices == null || gpsDevices.isEmpty()) {
                return AjaxResult.error("该船只未配置GPS设备");
            }

            // 按优先级排序，取优先级最高的设备（cost值最大的）
            // 如果没有设置优先级的设备，则取第一个设备
            Device selectedGpsDevice = gpsDevices.stream()
                    .filter(device -> device.getCost() != null)
                    .max(Comparator.comparing(Device::getCost))
                    .orElse(gpsDevices.get(0));

            if (selectedGpsDevice == null || StringUtils.isBlank(selectedGpsDevice.getCode())) {
                return AjaxResult.error("未找到有效的GPS设备");
            }

            // 获取GPS轨迹数据
            List<List<Double>> gpsTrackData = gpsService.getGpsData(shipSn, selectedGpsDevice.getCode(),
                    interval, startTime, endTime);

            return AjaxResult.success(gpsTrackData);

        } catch (Exception e) {
            return AjaxResult.error("获取轨迹数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取设备历史数据
     * @param shipId 船只ID
     * @param deviceId 设备ID
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param interval 数据间隔
     * @return 设备历史数据
     */
    @GetMapping("/historyData")
    public AjaxResult getHistoryData(Long shipId, Long deviceId, Long startTime, Long endTime, Integer interval) {
        try {
            // 参数验证
            if (shipId == null) {
                return AjaxResult.error("船只ID不能为空");
            }
            if (deviceId == null) {
                return AjaxResult.error("设备ID不能为空");
            }
            if (startTime == null || endTime == null) {
                return AjaxResult.error("开始时间和结束时间不能为空");
            }
            if (startTime >= endTime) {
                return AjaxResult.error("开始时间必须小于结束时间");
            }
            if (interval == null || interval <= 0) {
                return AjaxResult.error("数据间隔必须大于0");
            }

            // 查询船只信息
            Ship ship = shipService.selectShipByShipId(shipId);
            if (ship == null) {
                return AjaxResult.error("未找到指定的船只信息");
            }

            // 查询设备信息
            Device device = deviceService.selectDeviceById(deviceId);
            if (device == null) {
                return AjaxResult.error("未找到指定的设备信息");
            }

            String shipSn = ship.getSn();
            if (StringUtils.isBlank(shipSn)) {
                return AjaxResult.error("船只序列号为空");
            }

            String deviceCode = device.getCode();
            if (StringUtils.isBlank(deviceCode)) {
                return AjaxResult.error("设备编号为空");
            }

            Integer deviceType = device.getType();
            if (deviceType == null) {
                return AjaxResult.error("设备类型为空");
            }

            // 根据设备类型获取对应的历史数据
            if (DeviceTypeEnum.GPS.getValue().equals(deviceType)) {
                List<List<Double>> gpsData = gpsService.getGpsData(shipSn, deviceCode, interval, startTime, endTime);
                return AjaxResult.success(gpsData);
            } else if (DeviceTypeEnum.AWS.getValue().equals(deviceType)) {
                Object awsData = awsService.queryByTime(shipSn, deviceCode, interval, startTime, endTime);
                return AjaxResult.success(awsData);
            } else if (DeviceTypeEnum.ATTITUDE.getValue().equals(deviceType)) {
                Object attitudeData = attitudeService.queryByTime(shipSn, deviceCode, interval, startTime, endTime);
                return AjaxResult.success(attitudeData);
            } else {
                return AjaxResult.error("不支持的设备类型：" + deviceType);
            }

        } catch (Exception e) {
            return AjaxResult.error("获取历史数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取船只实时数据
     * @param shipId 船只ID
     * @param type 数据包类型，多个类型用#分隔，如：0A02#0A03#0B02#0B03#0E01#0E02#0E03
     * @return 实时数据响应
     */
    @GetMapping("/test/realtimeData")
    public AjaxResult getRealtimeData(Long shipId, String type) {
        try {
            // 参数验证
            if (shipId == null) {
                return AjaxResult.error("船只ID不能为空");
            }
            if (StringUtils.isBlank(type)) {
                return AjaxResult.error("数据包类型不能为空");
            }

            // 查询船只信息
            Ship ship = shipService.selectShipByShipId(shipId);
            if (ship == null) {
                return AjaxResult.error("未找到指定的船只信息");
            }

            String shipSn = ship.getSn();
            if (StringUtils.isBlank(shipSn)) {
                return AjaxResult.error("船只序列号为空");
            }

            // 解析数据包类型
            String[] dataTypes = type.split("#");
            if (dataTypes.length == 0) {
                return AjaxResult.error("数据包类型格式错误");
            }

            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();

            // 获取部门ID（用于某些数据包查询）
            String deptId = ship.getDeptId() != null ? ship.getDeptId().toString() : null;

            // 遍历每个数据包类型，获取对应数据
            for (String dataType : dataTypes) {
                String trimmedType = dataType.trim();
                if (StringUtils.isNotBlank(trimmedType)) {
                    Object data = realtimeService.getSrceenLatestData(deptId, shipSn, trimmedType);
                    responseData.put(trimmedType, data != null ? data : new HashMap<>());
                }
            }

            return AjaxResult.success("操作成功", responseData);

        } catch (Exception e) {
            logger.error("获取实时数据失败：", e);
            return AjaxResult.error("获取实时数据失败：" + e.getMessage());
        }
    }


    /**
     * 设备历史数据列表接口
     */
    @PreAuthorize("@ss.hasPermi('business:cruise:query')")
    @GetMapping(value = "/warningData")
    public AjaxResult warningData(@RequestParam("shipId") String shipId,
                                  @RequestParam (value = "startTime",required = false) String startTime,@RequestParam (value = "endTime",required = false) String endTime)
    {
        //System.out.println("参数#2##:"+shipId+"#######"+stime+"#######"+etime);
        if(shipId==null || "".equals(shipId)|| "null".equals(shipId)){
            return AjaxResult.error("参数错误！");
        }
        Ship ship = iShipService.selectShipByShipId(Long.parseLong(shipId));
        if(ship==null){return AjaxResult.error("参数错误！");}
        BuShipWarning buShipWarning = new BuShipWarning();
        buShipWarning.setSn(ship.getSn());
        //支持多种时间格式
        String stime = "";
        String etime = "";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if(( startTime+"" ).length()>9 && ( startTime+"" ).indexOf("-")!=-1 ){
            stime = startTime+"";
        }else{
            stime = dateFormat.format(new Date(Long.parseLong(startTime)));
        }
        if(( endTime+"" ).length()>9 && ( endTime+"" ).indexOf("-")!=-1 ){
            etime = endTime;
        }else{
            etime = dateFormat.format(new Date(Long.parseLong(endTime)));
        }
        if(( stime+"" ).length()>9 ){
            buShipWarning.setStime(stime);
        }
        if(( etime+"" ).length()>9 ){
            buShipWarning.setEtime(etime);
        }
        List<BuShipWarning> list = buShipWarningService.selectBuShipWarningList(buShipWarning);

        return success(list);
    }

}
