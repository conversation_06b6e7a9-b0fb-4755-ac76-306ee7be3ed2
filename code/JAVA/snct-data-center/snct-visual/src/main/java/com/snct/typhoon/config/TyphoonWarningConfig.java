package com.snct.typhoon.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName: TyphoonWarningConfig
 * @Description: 台风预警配置类
 * @author: wzewei
 * @date: 2025-08-22 11:41
 */
@Configuration
@ConfigurationProperties(prefix = "typhoon.warning")
public class TyphoonWarningConfig {
    
    /**
     * 是否启用台风预警功能
     */
    private boolean enabled = true;
    
    /**
     * 是否启用定时任务调度器
     */
    private boolean schedulerEnabled = true;
    
    /**
     * 预警检查间隔（毫秒）
     */
    private long checkInterval = 600000L; // 10分钟
    
    /**
     * 预警去重时间窗口（毫秒）
     */
    private long dedupWindow = 3600000L; // 1小时
    
    /**
     * 紧急预警半径（公里）
     */
    private double criticalRadius = 50.0;
    
    /**
     * 高级预警半径（公里）
     */
    private double highRadius = 100.0;
    
    /**
     * 中级预警半径（公里）
     */
    private double mediumRadius = 200.0;
    
    /**
     * 是否启用预警通知
     */
    private boolean notificationEnabled = true;
    
    /**
     * 过期预警记录保留天数
     */
    private int expiredWarningRetentionDays = 30;
    
    // Getter and Setter methods
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public boolean isSchedulerEnabled() {
        return schedulerEnabled;
    }
    
    public void setSchedulerEnabled(boolean schedulerEnabled) {
        this.schedulerEnabled = schedulerEnabled;
    }
    
    public long getCheckInterval() {
        return checkInterval;
    }
    
    public void setCheckInterval(long checkInterval) {
        this.checkInterval = checkInterval;
    }
    
    public long getDedupWindow() {
        return dedupWindow;
    }
    
    public void setDedupWindow(long dedupWindow) {
        this.dedupWindow = dedupWindow;
    }
    
    public double getCriticalRadius() {
        return criticalRadius;
    }
    
    public void setCriticalRadius(double criticalRadius) {
        this.criticalRadius = criticalRadius;
    }
    
    public double getHighRadius() {
        return highRadius;
    }
    
    public void setHighRadius(double highRadius) {
        this.highRadius = highRadius;
    }
    
    public double getMediumRadius() {
        return mediumRadius;
    }
    
    public void setMediumRadius(double mediumRadius) {
        this.mediumRadius = mediumRadius;
    }
    
    public boolean isNotificationEnabled() {
        return notificationEnabled;
    }
    
    public void setNotificationEnabled(boolean notificationEnabled) {
        this.notificationEnabled = notificationEnabled;
    }
    
    public int getExpiredWarningRetentionDays() {
        return expiredWarningRetentionDays;
    }
    
    public void setExpiredWarningRetentionDays(int expiredWarningRetentionDays) {
        this.expiredWarningRetentionDays = expiredWarningRetentionDays;
    }
    
    @Override
    public String toString() {
        return "TyphoonWarningConfig{" +
                "enabled=" + enabled +
                ", schedulerEnabled=" + schedulerEnabled +
                ", checkInterval=" + checkInterval +
                ", dedupWindow=" + dedupWindow +
                ", criticalRadius=" + criticalRadius +
                ", highRadius=" + highRadius +
                ", mediumRadius=" + mediumRadius +
                ", notificationEnabled=" + notificationEnabled +
                ", expiredWarningRetentionDays=" + expiredWarningRetentionDays +
                '}';
    }
}
