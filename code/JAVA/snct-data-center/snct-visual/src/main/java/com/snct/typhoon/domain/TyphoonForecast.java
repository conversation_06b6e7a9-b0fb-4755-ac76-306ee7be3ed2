package com.snct.typhoon.domain;


import com.snct.common.core.domain.BaseEntity;

import java.util.List;

/**
 * @ClassName: TyphoonForecast
 * @Description: 台风点位预测
 * @author: wzewei
 * @date: 2025-08-19 16:40:15
 */
public class TyphoonForecast extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 台风编号
     */
    private String tfid;

    /**
     * 台风点位
     */
    private Long typhoonPointId;

    /**
     * 预测的点位
     */
    private List<TyphoonForecastPoint> forecastPoints;

    /**
     * 来源
     */
    private String tm;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTfid() {
        return tfid;
    }

    public void setTfid(String tfid) {
        this.tfid = tfid;
    }

    public Long getTyphoonPointId() {
        return typhoonPointId;
    }

    public void setTyphoonPointId(Long typhoonPointId) {
        this.typhoonPointId = typhoonPointId;
    }

    public List<TyphoonForecastPoint> getForecastPoints() {
        return forecastPoints;
    }

    public void setForecastPoints(List<TyphoonForecastPoint> forecastPoints) {
        this.forecastPoints = forecastPoints;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }
}
