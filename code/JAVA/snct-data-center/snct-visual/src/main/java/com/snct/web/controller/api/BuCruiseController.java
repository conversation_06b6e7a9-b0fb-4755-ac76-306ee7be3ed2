package com.snct.web.controller.api;

import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.page.TableDataInfo;
import com.snct.common.enums.BusinessType;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.system.domain.BuCruise;
import com.snct.system.domain.BuDeviceConfig;
import com.snct.system.domain.BuShipWarning;
import com.snct.system.domain.Ship;
import com.snct.system.service.IBuCruiseService;
import com.snct.system.service.IBuDeviceConfigService;
import com.snct.system.service.IBuShipWarningService;
import com.snct.system.service.IShipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 航次Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/api/cruise")
public class BuCruiseController extends BaseController
{
    @Autowired
    private IBuCruiseService buCruiseService;
    @Autowired
    private IShipService iShipService;
    @Autowired
    private IBuDeviceConfigService buDeviceConfigService;
    /**
     * 查询航次列表
     */
    @PreAuthorize("@ss.hasPermi('business:cruise:list')")
    @GetMapping("/list/{shipId}")
    public AjaxResult list(@PathVariable("shipId") String shipId)
    {
        if(shipId==null || "".equals(shipId)|| "null".equals(shipId)){
            return AjaxResult.error("参数错误！");
        }
        Ship ship = iShipService.selectShipByShipId(Long.parseLong(shipId));
        if(ship==null){return AjaxResult.error("参数错误！");}
        BuCruise buCruise = new BuCruise();
        buCruise.setSn(ship.getSn());
        List<BuCruise> list = buCruiseService.selectBuCruiseList(buCruise);
        List relist = new ArrayList();
        for(BuCruise cruise : list){
            Map map = new HashMap();
            map.put("cruiseId",cruise.getCruiseId());
            map.put("code",cruise.getCode());
            relist.add(map);
        }
        return AjaxResult.success(relist);
    }

    /**
     * 获取航次详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:cruise:query')")
    @GetMapping(value = "/cruiseInfo/{cruiseId}")
    public AjaxResult getInfo(@PathVariable("cruiseId") Long cruiseId)
    {
        BuCruise cruise = buCruiseService.selectBuCruiseByCruiseId(cruiseId);
        if(cruise==null){return AjaxResult.error("参数错误！");}
        Map map = new HashMap();
        map.put("cruiseId",cruise.getCruiseId());
        map.put("code",cruise.getCode());
        map.put("startTime",cruise.getStartTime());
        map.put("endTime",cruise.getFinishTime());

          //测试
//        BuDeviceConfig buDeviceConfig = new BuDeviceConfig();
//        buDeviceConfig.setConfigKey("asda");
//        buDeviceConfig.setDeviceId(433l);
//        BuDeviceConfig config = buDeviceConfigService.selectBydeviceIdAndKey(buDeviceConfig);

        return success(map);
    }

}
