<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.snct.typhoon.mapper.TyphoonForecastPointMapper">

    <resultMap type="TyphoonForecastPoint" id="TyphoonForecastPointResult">
        <id property="id" column="id"/>
        <result property="tfid" column="tfid"/>
        <result property="typhoonForecastId" column="typhoon_forecast_id"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="power" column="power"/>
        <result property="pressure" column="pressure"/>
        <result property="speed" column="speed"/>
        <result property="strong" column="strong"/>
        <result property="time" column="time"/>
        <result property="timeStamp" column="time_stamp"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTyphoonForecastPointVo">
        select id, tfid,typhoon_forecast_id, lat, lng, power, pressure, speed, strong, time, time_stamp, create_time, update_time
		from typhoon_forecast_point
    </sql>

    <select id="selectTyphoonForecastPoint" parameterType="TyphoonForecastPoint" resultMap="TyphoonForecastPointResult">
        <include refid="selectTyphoonForecastPointVo"/>
        <where>
            <if test="typhoonForecastId !=null">
                and typhoon_forecast_id = #{typhoonForecastId}
            </if>
        </where>
    </select>

    <select id="selectTyphoonForecastPointList" parameterType="TyphoonForecastPoint" resultMap="TyphoonForecastPointResult">
        <include refid="selectTyphoonForecastPointVo"/>
        <where>
            <if test="tfid !=null">
                and tfid = #{tfid}
            </if>
            <if test="typhoonForecastId !=null">
                and typhoon_forecast_id = #{typhoonForecastId}
            </if>
        </where>
    </select>

    <insert id="addTyphoonForecastPoint" parameterType="TyphoonForecastPoint" useGeneratedKeys="true" keyProperty="id">
        insert into typhoon_forecast_point (
        <if test="tfid != null and tfid != '' ">tfid,</if>
        <if test="typhoonForecastId != null and typhoonForecastId != '' ">typhoon_forecast_id,</if>
        <if test="lat != null and lat != ''">lat,</if>
        <if test="lng != null and lng != ''">lng,</if>
        <if test="power != null and power != '' ">power,</if>
        <if test="pressure != null and pressure != ''">pressure,</if>
        <if test="speed != null and speed != ''">speed,</if>
        <if test="strong != null and strong != ''">strong,</if>
        <if test="time != null and time != ''">time,</if>
        <if test="timeStamp != null and timeStamp != ''">time_stamp,</if>
        create_time
        )values(
        <if test="tfid != null and tfid != ''">#{tfid},</if>
        <if test="typhoonForecastId != null and typhoonForecastId != ''">#{typhoonForecastId},</if>
        <if test="lat != null and lat != ''">#{lat},</if>
        <if test="lng != null and lng != ''">#{lng},</if>
        <if test="power != null and power != ''">#{power},</if>
        <if test="pressure != null and pressure != ''">#{pressure},</if>
        <if test="speed != null and speed != ''">#{speed},</if>
        <if test="strong != null and strong != ''">#{strong},</if>
        <if test="time != null and time != ''">#{time},</if>
        <if test="timeStamp != null and timeStamp != ''">#{timeStamp},</if>
        sysdate()
        )
    </insert>

    <update id="updateTyphoonForecastPoint" parameterType="TyphoonForecastPoint">
        update typhoon_forecast_point
        <set>
            <if test="tfid != null and tfid != ''">tfid = #{tfid},</if>
            <if test="typhoonForecastId != null and typhoonForecastId != ''">typhoon_forecast_id = #{typhoonForecastId},</if>
            <if test="lat != null and lat != ''">lat = #{lat},</if>
            <if test="lng != null and lng != ''">lng = #{lng},</if>
            <if test="power != null and power != ''">power = #{power},</if>
            <if test="pressure != null and pressure != ''">pressure = #{pressure},</if>
            <if test="speed != null and speed != ''">speed = #{speed},</if>
            <if test="strong != null and strong != ''">strong = #{strong},</if>
            <if test="time != null and time != ''">time = #{time},</if>
            <if test="timeStamp != null and timeStamp != ''">time_stamp = #{timeStamp},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

</mapper>