<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.snct.typhoon.mapper.TyphoonForecastMapper">

    <resultMap type="TyphoonForecast" id="TyphoonForecastResult">
        <id property="id" column="id"/>
        <result property="tfid" column="tfid"/>
        <result property="typhoonPointId" column="typhoon_point_id"/>
        <result property="tm" column="tm"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTyphoonForecastVo">
        select id, tfid, typhoon_point_id, tm, create_time, update_time
		from typhoon_forecast
    </sql>

    <select id="selectTyphoonForecast" parameterType="TyphoonForecast" resultMap="TyphoonForecastResult">
        <include refid="selectTyphoonForecastVo"/>
        <where>
            <if test="typhoon_point_id !=null">
                and typhoon_point_id = #{typhoonPointId}
            </if>
        </where>
    </select>

    <select id="selectTyphoonForecastList" parameterType="TyphoonForecast" resultMap="TyphoonForecastResult">
        <include refid="selectTyphoonForecastVo"/>
        <where>
            <if test="tfid !=null">
                and tfid = #{tfid}
            </if>
            <if test="typhoonPointId !=null">
                and typhoon_point_id = #{typhoonPointId}
            </if>
            <if test="tm != null and tm != ''">
                AND tm like concat('%', #{tm}, '%')
            </if>
        </where>
    </select>

    <insert id="addTyphoonForecast" parameterType="TyphoonForecast" useGeneratedKeys="true" keyProperty="id">
        insert into typhoon_forecast (
        <if test="tfid != null and tfid != '' ">tfid,</if>
        <if test="typhoonPointId != null and typhoonPointId != '' ">typhoon_point_id,</if>
        <if test="tm != null and tm != '' ">tm,</if>
        create_time
        )values(
        <if test="tfid != null and tfid != ''">#{tfid},</if>
        <if test="typhoonPointId != null and typhoonPointId != ''">#{typhoonPointId},</if>
        <if test="tm != null and tm != ''">#{tm},</if>
        sysdate()
        )
    </insert>

    <update id="updateTyphoonForecast" parameterType="TyphoonForecast">
        update typhoon_forecast
        <set>
            <if test="tfid != null and tfid != ''">tfid = #{tfid},</if>
            <if test="typhoonPointId != null and typhoonPointId != ''">typhoon_point_id = #{typhoonPointId},</if>
            <if test="tm != null and tm != ''">tm = #{tm},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

</mapper>