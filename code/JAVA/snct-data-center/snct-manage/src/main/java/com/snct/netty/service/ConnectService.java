package com.snct.netty.service;

import com.alibaba.fastjson.JSONObject;
import com.snct.common.utils.spring.SpringUtils;
import com.snct.dctcore.commoncore.domain.device.DeviceEntity;
import com.snct.dctcore.commoncore.domain.device.TransferAttributeEntity;
import com.snct.dctcore.commoncore.domain.transfer.TransferPackage;
import com.snct.dctcore.commoncore.enums.PackageTypeEnum;
import com.snct.dctcore.commoncore.utils.JsonUtil;
import com.snct.dctcore.commoncore.utils.NumUtils;
import com.snct.netty.server.UdpServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;

//@Service
public class ConnectService {

    private Logger logger = LoggerFactory.getLogger(ConnectService.class);
    /**
     * 岸上UPD监听 端口
     */
    @Value("${flystone.udpPort}")
    private Integer udpPort;

    @Resource
    private DataHandleService dataHandleService;

    //@Autowired
    //private LaputaApi laputaApi;

    @Autowired
    private SendService sendService;

    /**
     * 创建UDP接收服务
     */
    public void initUdpServer() {
        TaskExecutor threadPool = SpringUtils.getBean("threadPoolTaskExecutor");
        threadPool.execute(() -> {
            new UdpServer(udpPort);
        });
    }


    /**
     * 处理接收到的消息
     *
     * @param transferPackage
     */
    public void handleMessage(TransferPackage transferPackage) {
        try {
            if (transferPackage == null) {
                return;
            }
            // 保持连接的信息,不做处理
            if (PackageTypeEnum.CONNECT_KEEP.getValue().equals(transferPackage.getPackageType())) {
                logger.info("接到数据,--保持连接---{}", JsonUtil.obj2String(transferPackage));
                return;
            }

            // 处理传感器数据
            if (PackageTypeEnum.DEVICE_DATA.getValue().equals(transferPackage.getPackageType())) {
                if (dataHandleService.removeNonExistData(transferPackage)) {
                    return;
                }
                String[] sns = transferPackage.getSn().split(",");
                String beidouNm = sns[1];//北斗的编号
                if (beidouNm.equalsIgnoreCase("1111111")) {
                    return;
                }

                transferPackage.setSn(beidouNm);//真正的SN
                if (beidouNm.equalsIgnoreCase("8888888")){
                    //第三方无用数据
                    //addTransferAttributeEntity(transferPackage);
                }else{
//                    //根据编号以及设备编码查找设备表是否已经存在
                    DeviceEntity param = new DeviceEntity();
                    param.setSn(beidouNm);
                    param.setCode(transferPackage.getDeviceCode());
                    //DeviceEntity deviceEntity = laputaApi.getDevice(param);
                    DeviceEntity deviceEntity = null;
                    if (deviceEntity == null) {
                        //如果不存在则新增一条设备以及设备属性
                        //addTransferAttributeEntity(transferPackage);
                        Thread.sleep(10*1000);
                    }
                }

                //如果存在则往下走
                dataHandleService.receiveMessage(transferPackage);
            }

        } catch (Exception e) {
            logger.error("处理数据失败，--{}", e);
        }
    }

    /**
     * 解析传输的UDP包
     *
     * @param msg
     * @return
     */
    public TransferPackage analysisPackage(byte[] msg) throws Exception {
        int length = msg.length;
        if (length < 28) {
            return null;
        }

        TransferPackage transferPackage = new TransferPackage();

        int offset = 0;

        // 船只序列号
        transferPackage.setSn(new String(NumUtils.splitBytes(msg, offset, 14)));
        offset += 14;

        // 设备类型
        transferPackage.setPackageType((int) NumUtils.splitByte(msg, offset));
        offset++;

        // 统一编码
        transferPackage.setCommandNum(NumUtils.byteArray2Int(NumUtils.splitBytes(msg, offset, 4)));
        offset += 4;

        //时间戳
        transferPackage.setTime(NumUtils.bytesToLong(NumUtils.splitBytes(msg, offset, 8)));
        offset += 8;

        // 是否补发
        transferPackage.setIsRepair((int) NumUtils.splitByte(msg, offset));
        offset++;

        if (PackageTypeEnum.DEVICE_DATA.getValue().equals(transferPackage.getPackageType())) {
            // 设备类型
            transferPackage.setDeviceType((int) NumUtils.splitByte(msg, offset));
            offset++;
        }

        // 设备编码
        transferPackage.setDeviceCode(new String(NumUtils.splitBytes(msg, offset, 4)));
        offset += 4;

        if (PackageTypeEnum.SNAPSHOT_DATA.getValue().equals(transferPackage.getPackageType())) {
            // 拆包总数
            transferPackage.setUnpackingTotal(NumUtils.byteArray2Int(NumUtils.splitBytes(msg, offset, 4)));
            offset += 4;

            // 拆包编号
            transferPackage.setUnpackingNum(NumUtils.byteArray2Int(NumUtils.splitBytes(msg, offset, 4)));
            offset += 4;
        }

        // 消息主体
        transferPackage.setMessage(new String(NumUtils.splitBytes(msg, offset, length - offset)));

        return transferPackage;
    }

    /**
     * 批量添加设备属性表数据
     *
     * @param transferPackage
     */
//    public synchronized void addTransferAttributeEntity(TransferPackage transferPackage) {
//        if (transferPackage.getSn().equalsIgnoreCase("0420033")||transferPackage.getSn().equalsIgnoreCase("0420032")||transferPackage.getSn().equalsIgnoreCase("4578426")||transferPackage.getSn().equalsIgnoreCase("5784264")){
//            return;
//        }
//        DeviceEntity deviceEntity = new DeviceEntity();
//        deviceEntity.setCode(transferPackage.getDeviceCode());
//        deviceEntity.setSn(transferPackage.getSn());
//        deviceEntity.setType(transferPackage.getDeviceType());
//        deviceEntity.setEnable(1);
//        deviceEntity.setCreateBy("sync");
//        deviceEntity.setTransferStatus(1);
//        deviceEntity.setConnectStatus(1);
//        deviceEntity.setConnectType(1);
//        deviceEntity.setCompartment(120);
//        if (transferPackage.getDeviceType() == 38) {
//            deviceEntity.setName("气象仪");
//            TransferAttributeEntity transferAttributeEntity = new TransferAttributeEntity();
//            transferAttributeEntity.setDeviceCode(transferPackage.getDeviceCode());
//            transferAttributeEntity.setSn(transferPackage.getSn());
//            String[] strings = new String[]{"utcTime","relativeWindSpeed","relativeWind","airTemperature","humidity","pointTem","pressure","qfe","qnh","dp"};
//            transferAttributeEntity.setCreateBy("sync");
//            transferAttributeEntity.setTransferAttributes(strings);
//            laputaApi.addTransfer(transferAttributeEntity);
//            //气象仪
//        } else if (transferPackage.getDeviceType() == 32) {
//            deviceEntity.setName("GPS定位信息");
//            TransferAttributeEntity transferAttributeEntity = new TransferAttributeEntity();
//            transferAttributeEntity.setDeviceCode(transferPackage.getDeviceCode());
//            transferAttributeEntity.setSn(transferPackage.getSn());
//            String[] strings = new String[]{"utcTime","latitude","latitudeHemisphere","longitude","longitudeHemisphere"};
//            transferAttributeEntity.setCreateBy("sync");
//            transferAttributeEntity.setTransferAttributes(strings);
//            laputaApi.addTransfer(transferAttributeEntity);
//            //GPS
//        }else if (transferPackage.getDeviceType() == 33) {
//            deviceEntity.setName("姿态仪");
//            TransferAttributeEntity transferAttributeEntity = new TransferAttributeEntity();
//            transferAttributeEntity.setDeviceCode(transferPackage.getDeviceCode());
//            transferAttributeEntity.setSn(transferPackage.getSn());
////            if (transferPackage.getSn().split("@").length>1){
////                deviceEntity.setShipName(transferPackage.getSn().split("@")[1]);
////                transferAttributeEntity.setShipName(transferPackage.getSn().split("@")[1]);
////            }
//            String[] values = transferPackage.getMessage().split(",", -1);
//            if (values != null && values.length>0) {
//                deviceEntity.setShipName(values[5]);
//            }
//            String[] strings = new String[]{"utcTime","roll","pitch","heave","heading","stationName"};
//            transferAttributeEntity.setCreateBy("sync");
//            transferAttributeEntity.setTransferAttributes(strings);
//            laputaApi.addTransfer(transferAttributeEntity);
//            //姿态仪
//
//        }
//        logger.info("device{}", JSONObject.toJSONString(deviceEntity));
//
//        laputaApi.addDevice(deviceEntity);
//    }


}