package com.snct.service;

import com.alibaba.fastjson.JSONObject;
import com.snct.common.core.redis.RedisCache;
import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.system.domain.Device;
import com.snct.system.service.impl.DeviceServiceImpl;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @ClassName: ConsumeService
 * @Description: Kafka消费者服务
 * @author: wzewei
 * @date: 2025-08-12 09:17
 */
@Component
public class ConsumeService {

    public final static Logger logger = LoggerFactory.getLogger(ConsumeService.class);

    @Autowired
    private StoreService storeService;

    @Autowired
    private DeviceServiceImpl deviceService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 接收topic消息，并把数据存入redis中
     *
     * @param record
     */
    @KafkaListener(topics = {"snct_device_source_data_topic"}, groupId = "data_center_v1")
    public void analysisDataConsumer(ConsumerRecord<?, ?> record) {
        Optional<?> sourceMessage = Optional.ofNullable(record.value());
        if (sourceMessage.isPresent()) {
            Object message = sourceMessage.get();
            KafkaMessage kafkaMessage = JSONObject.parseObject(message.toString(), KafkaMessage.class);
            logger.info("保存到redis的时间----{}", record.timestamp());

            //DeviceEntity deviceEntity = redisCache.getCacheObject("DEVICE" + kafkaMessage.getCode());
            Device device = deviceService.selectDeviceBySnAndCode(kafkaMessage.getSn(), kafkaMessage.getCode());

            if (device != null) {
                if (device.getEnable() == 1) {
                    //存储状态开启-保存到hbase
                    storeService.save2Hbase(kafkaMessage);
                    //hbase存储完-往redis传输数据时记录当前最新时间，不断覆盖更新
                    //redisCache.setCacheObject(kafkaMessage.getCode(), System.currentTimeMillis());
                }
            }

        }
    }
}
