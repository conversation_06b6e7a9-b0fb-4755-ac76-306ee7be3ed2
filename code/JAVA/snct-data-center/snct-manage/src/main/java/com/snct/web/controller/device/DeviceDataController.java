package com.snct.web.controller.device;

import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.service.DeviceDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @ClassName: DeviceDataController
 * @Description: 设备数据控制器 - 提供设备最新数据查询接口
 * @author: wzewei
 * @date: 2025-08-21 10:30
 */
@RestController
@RequestMapping("/device/data")
public class DeviceDataController extends BaseController {

    @Autowired
    private DeviceDataService deviceDataService;

    /**
     * 获取设备最新数据
     *
     * @param sn 设备序列号
     * @param code 设备代码
     * @return 设备最新数据
     */
    @GetMapping("/latest")
    public AjaxResult getLatestData(@RequestParam String sn, @RequestParam String code) {
        try {
            String data = deviceDataService.getLatestDeviceData(sn, code);
            Long time = deviceDataService.getLatestDeviceTime(sn, code);
            
            if (data != null) {
                return AjaxResult.success()
                    .put("data", data)
                    .put("timestamp", time)
                    .put("sn", sn)
                    .put("code", code);
            } else {
                return AjaxResult.error("未找到设备数据");
            }
        } catch (Exception e) {
            return AjaxResult.error("获取设备数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取船只所有设备的最新数据
     *
     * @param sn 船只序列号
     * @return 船只所有设备数据
     */
    @GetMapping("/ship/{sn}")
    public AjaxResult getShipAllData(@PathVariable String sn) {
        try {
            Map<String, String> data = deviceDataService.getAllDeviceDataBySn(sn);
            return AjaxResult.success()
                .put("sn", sn)
                .put("deviceCount", data.size())
                .put("devices", data);
        } catch (Exception e) {
            return AjaxResult.error("获取船只设备数据失败: " + e.getMessage());
        }
    }

    /**
     * 检查设备数据是否存在
     *
     * @param sn 设备序列号
     * @param code 设备代码
     * @return 检查结果
     */
    @GetMapping("/exists")
    public AjaxResult checkDataExists(@RequestParam String sn, @RequestParam String code) {
        try {
            boolean exists = deviceDataService.hasDeviceData(sn, code);
            return AjaxResult.success()
                .put("exists", exists)
                .put("sn", sn)
                .put("code", code);
        } catch (Exception e) {
            return AjaxResult.error("检查设备数据失败: " + e.getMessage());
        }
    }

    /**
     * 删除设备数据
     *
     * @param sn 设备序列号
     * @param code 设备代码
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    public AjaxResult deleteData(@RequestParam String sn, @RequestParam String code) {
        try {
            boolean success = deviceDataService.deleteDeviceData(sn, code);
            if (success) {
                return AjaxResult.success("设备数据删除成功");
            } else {
                return AjaxResult.error("设备数据删除失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("删除设备数据失败: " + e.getMessage());
        }
    }
}
