package com.snct.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.dctcore.commoncore.domain.hbase.*;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.hbasecore.utils.HBaseBeanUtil;
import com.snct.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @ClassName: DeviceDataService
 * @Description: 设备数据服务
 * @author: wzewei
 * @date: 2025-08-21 10:30
 */
@Service
public class DeviceDataService {

    private static final Logger logger = LoggerFactory.getLogger(DeviceDataService.class);

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;
    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    /**
     * 查询设备数据分页
     *
     * @param deviceType 设备类型
     * @param tableName  表名
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param currentPage 当前页码
     * @param pageSize    每页记录数
     * @param sort        排序字段
     * @return 设备数据分页结果
     */
    public Map<String, Object> queryDeviceDataPage(Integer deviceType, String tableName, Long startTime, Long endTime
            , int currentPage, int pageSize, String sort) {
        Class<?> clazz = getClassByType(deviceType);
        if (clazz == null) {
            return new HashMap<>();
        }
        try {
            Object template = clazz.newInstance();
            List<Object> list = Lists.newArrayList();
            Map<String, Object> queryResult = new HashMap<>();
            List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
            Integer total = hBaseDaoUtil.scanByStartStop4PageDesc(template, tableName, list, rowList, currentPage,
                    pageSize, sort);
            queryResult.put("data", list);
            queryResult.put("total", total);
            return queryResult;
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    /**
     * 根据rowkey查询设备详细记录信息
     *
     * @param tableName  表名
     * @param deviceType 设备类型
     * @param rowkeys    rowkey数组
     * @return 设备详细记录列表
     */
    public List<Object> getDeviceDetailsByRowKeys(String tableName, Integer deviceType, String... rowkeys) {
        List<Object> resultList = new ArrayList<>();

        if (StringUtils.isBlank(tableName) || deviceType == null || rowkeys == null || rowkeys.length == 0) {
            return resultList;
        }
        Class<?> clazz = getClassByType(deviceType);
        if (clazz == null) {
            return resultList;
        }
        try {
            List<Result> hbaseResults = hBaseDaoUtil.getResults(tableName, rowkeys);

            if (hbaseResults == null || hbaseResults.isEmpty()) {
                return resultList;
            }
            Object template = clazz.newInstance();
            for (Result result : hbaseResults) {
                if (result != null && !result.isEmpty()) {
                    try {
                        Object deviceObject = HBaseBeanUtil.resultToBean(result, template);
                        if (deviceObject != null) {
                            resultList.add(deviceObject);
                        }
                    } catch (Exception e) {
                        logger.error("将HBase结果转换为设备对象失败 - 表名: {}, 设备类型: {}, rowkey: {}, 错误: {}",
                                tableName, deviceType, result.getRow(), e.getMessage(), e);
                    }
                }
            }
        } catch (Exception e) {
            return new ArrayList<>();
        }
        return resultList;
    }

    /**
     * 获取设备最新数据
     *
     * @param sn   设备序列号
     * @param code 设备代码
     * @return 设备最新数据JSON字符串
     */
    public String getLatestDeviceData(String sn, String code) {
        try {
            String deviceKey = RedisParameter.DEVICE_DATA + sn + ":" + code;
            Object data = redisTemplate.opsForValue().get(deviceKey);
            return data != null ? data.toString() : null;
        } catch (Exception e) {
            logger.error("获取设备最新数据失败 - 设备: {}, 代码: {}, 错误: {}", sn, code, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取设备最新时间戳
     *
     * @param sn   设备序列号
     * @param code 设备代码
     * @return 最新时间戳
     */
    public Long getLatestDeviceTime(String sn, String code) {
        try {
            String timeKey = RedisParameter.DEVICE_LATEST_TIME + sn + ":" + code;
            Object time = redisTemplate.opsForValue().get(timeKey);
            return time != null ? Long.valueOf(time.toString()) : null;
        } catch (Exception e) {
            logger.error("获取设备最新时间戳失败 - 设备: {}, 代码: {}, 错误: {}", sn, code, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取设备最新数据对象
     *
     * @param sn         设备序列号
     * @param code       设备代码
     * @param deviceType 设备类型
     * @return 设备数据对象
     */
    public Object getLatestDeviceObject(String sn, String code, Integer deviceType) {
        try {
            String dataJson = getLatestDeviceData(sn, code);
            if (dataJson == null) {
                return null;
            }

            Class<?> clazz = getClassByType(deviceType);
            if (clazz == null) {
                return null;
            }

            return JSON.parseObject(dataJson, clazz);
        } catch (Exception e) {
            logger.error("获取设备最新数据对象失败 - 设备: {}, 代码: {}, 类型: {}, 错误: {}",
                    sn, code, deviceType, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取船只所有设备的最新数据
     *
     * @param sn 船只序列号
     * @return 设备数据Map，key为设备代码，value为设备数据JSON
     */
    public Map<String, String> getAllDeviceDataBySn(String sn) {
        Map<String, String> result = new HashMap<>();
        try {
            String pattern = RedisParameter.DEVICE_DATA + sn + ":*";
            Set<Object> keys = redisTemplate.keys(pattern);

            if (keys != null && !keys.isEmpty()) {
                for (Object key : keys) {
                    String keyStr = key.toString();
                    String code = keyStr.substring(keyStr.lastIndexOf(":") + 1);
                    Object data = redisTemplate.opsForValue().get(key);
                    if (data != null) {
                        result.put(code, data.toString());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("获取船只所有设备数据失败 - 船只: {}, 错误: {}", sn, e.getMessage(), e);
        }
        return result;
    }

    /**
     * 检查设备数据是否存在
     *
     * @param sn   设备序列号
     * @param code 设备代码
     * @return true-存在，false-不存在
     */
    public boolean hasDeviceData(String sn, String code) {
        try {
            String deviceKey = RedisParameter.DEVICE_DATA + sn + ":" + code;
            return Boolean.TRUE.equals(redisTemplate.hasKey(deviceKey));
        } catch (Exception e) {
            logger.error("检查设备数据是否存在失败 - 设备: {}, 代码: {}, 错误: {}", sn, code, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除设备数据
     *
     * @param sn   设备序列号
     * @param code 设备代码
     * @return true-删除成功，false-删除失败
     */
    public boolean deleteDeviceData(String sn, String code) {
        try {
            String deviceKey = RedisParameter.DEVICE_DATA + sn + ":" + code;
            String timeKey = RedisParameter.DEVICE_LATEST_TIME + sn + ":" + code;

            Boolean result1 = redisTemplate.delete(deviceKey);
            Boolean result2 = redisTemplate.delete(timeKey);

            return Boolean.TRUE.equals(result1) || Boolean.TRUE.equals(result2);
        } catch (Exception e) {
            logger.error("删除设备数据失败 - 设备: {}, 代码: {}, 错误: {}", sn, code, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据设备类型获取对应的类
     *
     * @param type 设备类型
     * @return 对应的类
     */
    private Class<?> getClassByType(Integer type) {
        switch (DeviceTypeEnum.getByValue(type)) {
            case GPS: {
                return GpsHbaseVo.class;
            }
            case AWS: {
                return AwsHbaseVo.class;
            }
            case ATTITUDE: {
                return AttitudeHbaseVo.class;
            }
            case PDU: {
                return PduHbaseVo.class;
            }
            case MODEM: {
                return ModemHbaseVo.class;
            }
            case AMPLIFIER: {
                return AmplifierHbaseVo.class;
            }
            default: {
                return null;
            }
        }
    }
}
