package com.snct.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.domain.BuDeviceConfig;
import com.snct.system.service.IBuDeviceConfigService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * 设备配置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@RestController
@RequestMapping("/business/deviceConfig")
public class BuDeviceConfigController extends BaseController
{
    @Autowired
    private IBuDeviceConfigService buDeviceConfigService;

    /**
     * 查询设备配置列表
     */
    @PreAuthorize("@ss.hasPermi('business:deviceConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(BuDeviceConfig buDeviceConfig)
    {
        startPage();
        List<BuDeviceConfig> list = buDeviceConfigService.selectBuDeviceConfigList(buDeviceConfig);
        return getDataTable(list);
    }

    /**
     * 导出设备配置列表
     */
    @PreAuthorize("@ss.hasPermi('business:deviceConfig:export')")
    @Log(title = "设备配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuDeviceConfig buDeviceConfig)
    {
        List<BuDeviceConfig> list = buDeviceConfigService.selectBuDeviceConfigList(buDeviceConfig);
        ExcelUtil<BuDeviceConfig> util = new ExcelUtil<BuDeviceConfig>(BuDeviceConfig.class);
        util.exportExcel(response, list, "设备配置数据");
    }

    /**
     * 获取设备配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:deviceConfig:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(buDeviceConfigService.selectBuDeviceConfigById(id));
    }

    /**
     * 新增设备配置
     */
    @PreAuthorize("@ss.hasPermi('business:deviceConfig:add')")
    @Log(title = "设备配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuDeviceConfig buDeviceConfig)
    {
        BuDeviceConfig config = new BuDeviceConfig();
        config.setConfigKey(buDeviceConfig.getConfigKey());
        config.setDeviceId(buDeviceConfig.getDeviceId());
        List<BuDeviceConfig> list =buDeviceConfigService.selectBuDeviceConfigList(config);
        if(list.size()>0) {
            return AjaxResult.error("数据已经存在");
        }
        return toAjax(buDeviceConfigService.insertBuDeviceConfig(buDeviceConfig));
    }

    /**
     * 修改设备配置
     */
    @PreAuthorize("@ss.hasPermi('business:deviceConfig:edit')")
    @Log(title = "设备配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuDeviceConfig buDeviceConfig)
    {
        return toAjax(buDeviceConfigService.updateBuDeviceConfig(buDeviceConfig));
    }

    /**
     * 删除设备配置
     */
    @PreAuthorize("@ss.hasPermi('business:deviceConfig:remove')")
    @Log(title = "设备配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(buDeviceConfigService.deleteBuDeviceConfigByIds(ids));
    }
}
