package com.snct.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.snct.system.domain.BuShipWarning;
import com.snct.system.service.IBuShipWarningService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * 船舶预警日志Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@RestController
@RequestMapping("/business/warning")
public class BuShipWarningController extends BaseController
{
    @Autowired
    private IBuShipWarningService buShipWarningService;

    /**
     * 查询船舶预警日志列表
     */
    @PreAuthorize("@ss.hasPermi('business:warning:list')")
    @GetMapping("/list")
    public TableDataInfo list(BuShipWarning buShipWarning)
    {
        startPage();
        List<BuShipWarning> list = buShipWarningService.selectBuShipWarningList(buShipWarning);
        return getDataTable(list);
    }

    /**
     * 导出船舶预警日志列表
     */
    @PreAuthorize("@ss.hasPermi('business:warning:export')")
    @Log(title = "船舶预警日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuShipWarning buShipWarning)
    {
        List<BuShipWarning> list = buShipWarningService.selectBuShipWarningList(buShipWarning);
        ExcelUtil<BuShipWarning> util = new ExcelUtil<BuShipWarning>(BuShipWarning.class);
        util.exportExcel(response, list, "船舶预警日志数据");
    }

    /**
     * 获取船舶预警日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:warning:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(buShipWarningService.selectBuShipWarningById(id));
    }

    /**
     * 新增船舶预警日志
     */
    @PreAuthorize("@ss.hasPermi('business:warning:add')")
    @Log(title = "船舶预警日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuShipWarning buShipWarning)
    {
        return toAjax(buShipWarningService.insertBuShipWarning(buShipWarning));
    }

    /**
     * 修改船舶预警日志
     */
    @PreAuthorize("@ss.hasPermi('business:warning:edit')")
    @Log(title = "船舶预警日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuShipWarning buShipWarning)
    {
        buShipWarning.setStatus(1l);
        return toAjax(buShipWarningService.updateBuShipWarning(buShipWarning));
    }

    /**
     * 删除船舶预警日志
     */
    @PreAuthorize("@ss.hasPermi('business:warning:remove')")
    @Log(title = "船舶预警日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(buShipWarningService.deleteBuShipWarningByIds(ids));
    }
}
