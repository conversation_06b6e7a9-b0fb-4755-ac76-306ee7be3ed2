package com.snct.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.domain.BuCruise;
import com.snct.system.service.IBuCruiseService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * 航次Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/business/cruise")
public class BuCruiseController extends BaseController
{
    @Autowired
    private IBuCruiseService buCruiseService;

    /**
     * 查询航次列表
     */
    @PreAuthorize("@ss.hasPermi('business:cruise:list')")
    @GetMapping("/list")
    public TableDataInfo list(BuCruise buCruise)
    {
        startPage();
        List<BuCruise> list = buCruiseService.selectBuCruiseList(buCruise);
        return getDataTable(list);
    }

    /**
     * 导出航次列表
     */
    @PreAuthorize("@ss.hasPermi('business:cruise:export')")
    @Log(title = "航次", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuCruise buCruise)
    {
        List<BuCruise> list = buCruiseService.selectBuCruiseList(buCruise);
        ExcelUtil<BuCruise> util = new ExcelUtil<BuCruise>(BuCruise.class);
        util.exportExcel(response, list, "航次数据");
    }

    /**
     * 获取航次详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:cruise:query')")
    @GetMapping(value = "/{cruiseId}")
    public AjaxResult getInfo(@PathVariable("cruiseId") Long cruiseId)
    {
        return success(buCruiseService.selectBuCruiseByCruiseId(cruiseId));
    }

    /**
     * 新增航次
     */
    @PreAuthorize("@ss.hasPermi('business:cruise:add')")
    @Log(title = "航次", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuCruise buCruise)
    {
        return toAjax(buCruiseService.insertBuCruise(buCruise));
    }

    /**
     * 修改航次
     */
    @PreAuthorize("@ss.hasPermi('business:cruise:edit')")
    @Log(title = "航次", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuCruise buCruise)
    {
        return toAjax(buCruiseService.updateBuCruise(buCruise));
    }

    /**
     * 删除航次
     */
    @PreAuthorize("@ss.hasPermi('business:cruise:remove')")
    @Log(title = "航次", businessType = BusinessType.DELETE)
	@DeleteMapping("/{cruiseIds}")
    public AjaxResult remove(@PathVariable Long[] cruiseIds)
    {
        return toAjax(buCruiseService.deleteBuCruiseByCruiseIds(cruiseIds));
    }
}
