package com.snct.web.controller.business;

import com.alibaba.fastjson2.JSONArray;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.domain.entity.SysDictData;
import com.snct.common.core.page.TableDataInfo;
import com.snct.common.enums.BusinessType;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.system.domain.Device;
import com.snct.system.domain.Ship;
import com.snct.system.service.IDeviceService;
import com.snct.system.service.IShipService;
import com.snct.system.service.ISysDictDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 设备Controller
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@RestController
@RequestMapping("/business/device")
public class DeviceController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(DeviceController.class);
    
    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private IShipService shipService;

    @Autowired
    private ISysDictDataService isysDictDataService;


    /**
     * 查询设备列表
     */
    @PreAuthorize("@ss.hasPermi('business:device:list')")
    @GetMapping("/list")
    public TableDataInfo list(Device device)
    {
        startPage();
        List<Device> list = deviceService.selectDeviceList(device);
        return getDataTable(list);
    }

    /**
     * 导出设备列表
     */
    @PreAuthorize("@ss.hasPermi('business:device:export')")
    @Log(title = "设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Device device)
    {
        List<Device> list = deviceService.selectDeviceList(device);
        ExcelUtil<Device> util = new ExcelUtil<Device>(Device.class);
        util.exportExcel(response, list, "设备数据");
    }

    /**
     * 获取设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:device:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(deviceService.selectDeviceById(id));
    }

    /**
     * 新增设备
     */
    @PreAuthorize("@ss.hasPermi('business:device:add')")
    @Log(title = "设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Device device)
    {
        return toAjax(deviceService.insertDevice(device));
    }

    /**
     * 修改设备
     */
    @PreAuthorize("@ss.hasPermi('business:device:edit')")
    @Log(title = "设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Device device)
    {
        try {
            // 获取修改前的设备配置
            Device oldDevice = deviceService.selectDeviceById(device.getId());

            // 更新设备配置
            int result = deviceService.updateDevice(device);

            if (result > 0) {
                // 检查是否需要重新加载串口配置
                if (needReloadSerialConfig(oldDevice, device)) {
                    logger.info("设备[{}]的串口相关配置发生变化，重新加载串口服务", device.getCode());
                    try {
                        logger.info("设备[{}]串口配置重新加载成功", device.getCode());
                    } catch (Exception e) {
                        logger.error("设备[{}]串口配置重新加载失败", device.getCode(), e);
                    }
                }

                // 检查是否需要重新加载PDU设备配置
                if (needReloadPduConfig(oldDevice, device)) {
                    logger.info("设备[{}]的PDU相关配置发生变化，重新加载PDU连接", device.getCode());
                    try {
                        logger.info("设备[{}]PDU配置重新加载成功", device.getCode());
                    } catch (Exception e) {
                        logger.error("设备[{}]PDU配置重新加载失败", device.getCode(), e);
                    }
                }

                // 检查是否需要重新加载Modem设备配置（Modem 不需要）
            }

            return toAjax(result);
        } catch (Exception e) {
            logger.error("修改设备配置失败", e);
            return error("修改设备配置失败：" + e.getMessage());
        }
    }

    /**
     * 启用设备
     */
    @PreAuthorize("@ss.hasPermi('business:device:edit')")
    @Log(title = "启用设备", businessType = BusinessType.UPDATE)
    @PostMapping("/enable/{id}")
    public AjaxResult enableDevice(@PathVariable Long id)
    {
        try {
            // 获取设备信息
            Device device = deviceService.selectDeviceById(id);
            if (device == null) {
                return error("设备不存在");
            }

            // 检查设备是否已经启用
            if (device.getEnable() != null && device.getEnable() == 1) {
                return success("设备已经是启用状态");
            }

            // 记录原始状态
            Integer oldEnable = device.getEnable();

            // 启用设备
            device.setEnable(1);
            int result = deviceService.updateDevice(device);

            if (result > 0) {
                logger.info("设备[{}]已启用，状态变更: {} -> {}", device.getCode(), oldEnable, 1);

                return success("设备启用成功");
            } else {
                return error("设备启用失败");
            }
        } catch (Exception e) {
            logger.error("启用设备失败", e);
            return error("启用设备失败：" + e.getMessage());
        }
    }

    /**
     * 禁用设备
     */
    @PreAuthorize("@ss.hasPermi('business:device:edit')")
    @Log(title = "禁用设备", businessType = BusinessType.UPDATE)
    @PostMapping("/disable/{id}")
    public AjaxResult disableDevice(@PathVariable Long id)
    {
        try {
            // 获取设备信息
            Device device = deviceService.selectDeviceById(id);
            if (device == null) {
                return error("设备不存在");
            }

            // 检查设备是否已经禁用
            if (device.getEnable() != null && device.getEnable() == 0) {
                return success("设备已经是禁用状态");
            }

            // 记录原始状态
            Integer oldEnable = device.getEnable();

            // 禁用设备
            device.setEnable(0);
            int result = deviceService.updateDevice(device);

            if (result > 0) {
                logger.info("设备[{}]已禁用，状态变更: {} -> {}", device.getCode(), oldEnable, 0);

                return success("设备禁用成功");
            } else {
                return error("设备禁用失败");
            }
        } catch (Exception e) {
            logger.error("禁用设备失败", e);
            return error("禁用设备失败：" + e.getMessage());
        }
    }

    /**
     * 删除设备
     */
    @PreAuthorize("@ss.hasPermi('business:device:remove')")
    @Log(title = "设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(deviceService.deleteDeviceByIds(ids));
    }



    public static Map<String,String> viewKey = new ConcurrentHashMap<String, String>();
    public static Map<String,String> viewValue = new ConcurrentHashMap<String,String>();
    /**
     * 同步设备消息
     */
    //@Log(title = "刷新设备数据", businessType = BusinessType.OTHER)
    @GetMapping("/refreshdata")
    public AjaxResult refreshdata(HttpServletRequest request)
    {
        String deviceid =  request.getParameter("deviceid");
        String code =   request.getParameter("code");
//        DeviceController.viewKey.clear();
//        DeviceController.viewValue.clear();
        DeviceController.viewKey.put(code,"1");
        Map<String,String> viewMap = DeviceController.viewValue;
        List<String> dkey = new ArrayList<String>();
        String re = "";
        for (Map.Entry<String, String> entry : viewMap.entrySet()) {
            System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
            re = "<div>"+entry.getValue()+"</div>" + re;
            dkey.add(entry.getKey());
        }
        for(String str : dkey){
            DeviceController.viewValue.remove(str);
        }
        return success(re+"###"+deviceid);
    }

    @GetMapping("/refreshdataclose")
    public AjaxResult refreshdataclose(HttpServletRequest request) throws InterruptedException {
        System.out.println("#####refreshdataclose####");
        DeviceController.viewKey.clear();
        Thread.sleep(500l);
        DeviceController.viewValue.clear();
        return success();
    }


    /**
     * 检查是否需要重新加载串口配置
     * 当串口相关的关键配置发生变化时返回true
     */
    private boolean needReloadSerialConfig(Device oldDevice, Device newDevice) {
        if (oldDevice == null || newDevice == null) {
            return false;
        }

        // 检查设备编号是否变化
        if (!java.util.Objects.equals(oldDevice.getCode(), newDevice.getCode())) {
            logger.debug("设备[{}]编号发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getCode(), newDevice.getCode());
            return true;
        }

        // 检查串口号是否变化
        if (!java.util.Objects.equals(oldDevice.getSerialPort(), newDevice.getSerialPort())) {
            logger.debug("设备[{}]串口号发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getSerialPort(), newDevice.getSerialPort());
            return true;
        }

        // 检查波特率是否变化
        if (!java.util.Objects.equals(oldDevice.getBaudRate(), newDevice.getBaudRate())) {
            logger.debug("设备[{}]波特率发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getBaudRate(), newDevice.getBaudRate());
            return true;
        }

        // 检查数据位是否变化
        if (!java.util.Objects.equals(oldDevice.getDataBits(), newDevice.getDataBits())) {
            logger.debug("设备[{}]数据位发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getDataBits(), newDevice.getDataBits());
            return true;
        }

        // 检查停止位是否变化
        if (!java.util.Objects.equals(oldDevice.getStopBits(), newDevice.getStopBits())) {
            logger.debug("设备[{}]停止位发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getStopBits(), newDevice.getStopBits());
            return true;
        }

        // 检查校验位是否变化
        if (!java.util.Objects.equals(oldDevice.getParity(), newDevice.getParity())) {
            logger.debug("设备[{}]校验位发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getParity(), newDevice.getParity());
            return true;
        }

        // 检查启用状态是否变化
        if (!java.util.Objects.equals(oldDevice.getEnable(), newDevice.getEnable())) {
            logger.debug("设备[{}]启用状态发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getEnable(), newDevice.getEnable());
            return true;
        }

        // 检查传输间隔是否变化
        if (!java.util.Objects.equals(oldDevice.getCompartment(), newDevice.getCompartment())) {
            logger.debug("设备[{}]传输间隔发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getCompartment(), newDevice.getCompartment());
            return true;
        }

        return false;
    }

    /**
     * 检查是否需要重新加载PDU设备配置
     * 当PDU设备的网络相关配置发生变化时返回true
     */
    private boolean needReloadPduConfig(Device oldDevice, Device newDevice) {
        if (oldDevice == null || newDevice == null) {
            return false;
        }

        // 只处理PDU设备（类型51）
        if (newDevice.getType() == null || newDevice.getType() != 51L) {
            return false;
        }

        // 检查IP地址是否变化
        if (!java.util.Objects.equals(oldDevice.getIp(), newDevice.getIp())) {
            logger.debug("PDU设备[{}]IP地址发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getIp(), newDevice.getIp());
            return true;
        }

        // 检查端口是否变化
        if (!java.util.Objects.equals(oldDevice.getPort(), newDevice.getPort())) {
            logger.debug("PDU设备[{}]端口发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getPort(), newDevice.getPort());
            return true;
        }

        // 检查启用状态是否变化
        if (!java.util.Objects.equals(oldDevice.getEnable(), newDevice.getEnable())) {
            logger.debug("PDU设备[{}]启用状态发生变化: {} -> {}",
                    newDevice.getCode(), oldDevice.getEnable(), newDevice.getEnable());
            return true;
        }

        return false;
    }


    /**
     * 查询设备列表
     */
    @PreAuthorize("@ss.hasPermi('business:device:list')")
    @GetMapping("/listBy/{sn}")
    public AjaxResult listBySn(@PathVariable("sn") String sn)
    {
        if(sn==null||"null".equals(sn)||"undefined".equals(sn)||"".equals(sn)){
            return AjaxResult.success(new JSONArray());
        }
        System.out.println("sn##ee##:"+sn);
        Device device = new Device();
        device.setSn(sn);
        List<Device> list = deviceService.selectDeviceList(device);
        JSONArray reList = new JSONArray();
        for(Device reDevice : list){
            Map map = new HashMap();
            map.put("name", reDevice.getName());
            map.put("deviceId", reDevice.getId());
            reList.add(map);
        }
        return AjaxResult.success(reList);
    }


    /**
     * 查询配置项列表
     */
    @PreAuthorize("@ss.hasPermi('business:device:list')")
    @GetMapping("/paramList")
    public AjaxResult paramList()
    {
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("device_param");
        List<SysDictData> list = isysDictDataService.selectDictDataList(sysDictData);
        JSONArray reList = new JSONArray();
        for(SysDictData reDictData : list){
            Map map = new HashMap();
            map.put("configKey", reDictData.getDictValue());
            map.put("name", reDictData.getDictLabel());
            reList.add(map);
        }
        return AjaxResult.success(reList);
    }

}
