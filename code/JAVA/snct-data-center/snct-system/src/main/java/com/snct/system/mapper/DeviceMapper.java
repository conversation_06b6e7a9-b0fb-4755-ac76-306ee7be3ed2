package com.snct.system.mapper;

import com.snct.system.domain.Device;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface DeviceMapper 
{
    /**
     * 查询设备
     * 
     * @param id 设备主键
     * @return 设备
     */
    public Device selectDeviceById(Long id);

    /**
     * 查询设备列表
     * 
     * @param device 设备
     * @return 设备集合
     */
    public List<Device> selectDeviceList(Device device);

    /**
     * 根据船舶SN和设备类型查询设备列表（只返回ID和名称）
     * 
     * @param sn 船舶SN
     * @param type 设备类型
     * @return 设备集合（只包含ID和名称）
     */
    public List<Device> selectSimpleDeviceListBySnAndType(@Param("sn") String sn, @Param("type") Long type);

    /**
     * 新增设备
     * 
     * @param device 设备
     * @return 结果
     */
    public int insertDevice(Device device);

    /**
     * 修改设备
     * 
     * @param device 设备
     * @return 结果
     */
    public int updateDevice(Device device);

    /**
     * 删除设备
     * 
     * @param id 设备主键
     * @return 结果
     */
    public int deleteDeviceById(Long id);

    /**
     * 批量删除设备
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceByIds(Long[] ids);

    /**
     * 查询设备信息
     *
     * @param device 设备信息
     * @return 设备信息
     */
    Device selectDevice(Device device);

    /**
     * 根据设备编号查询设备
     *
     * @param code 设备编号
     * @return 设备信息
     */
    Device selectDeviceByCode(String code);

    /**
     * 根据船舶SN和设备编号查询设备
     * @param sn
     * @param code
     * @return
     */
    Device selectDeviceBySnAndCode(@Param("sn") String sn, @Param("code") String code);
}
