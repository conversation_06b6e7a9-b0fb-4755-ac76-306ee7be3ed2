package com.snct.system.service.impl;

import java.util.List;

import com.snct.common.annotation.DataScope;
import com.snct.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuDeviceConfigMapper;
import com.snct.system.domain.BuDeviceConfig;
import com.snct.system.service.IBuDeviceConfigService;

/**
 * 设备配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Service
public class BuDeviceConfigServiceImpl implements IBuDeviceConfigService 
{
    @Autowired
    private BuDeviceConfigMapper buDeviceConfigMapper;

    /**
     * 查询设备配置
     * 
     * @param id 设备配置主键
     * @return 设备配置
     */
    @Override
    public BuDeviceConfig selectBuDeviceConfigById(Long id)
    {
        return buDeviceConfigMapper.selectBuDeviceConfigById(id);
    }

    @Override
    public BuDeviceConfig selectBydeviceIdAndKey(BuDeviceConfig buDeviceConfig)
    {
        return buDeviceConfigMapper.selectBydeviceIdAndKey(buDeviceConfig);
    }

    /**
     * 查询设备配置列表
     * 
     * @param buDeviceConfig 设备配置
     * @return 设备配置
     */
    @DataScope(deptAlias = "d")
    @Override
    public List<BuDeviceConfig> selectBuDeviceConfigList(BuDeviceConfig buDeviceConfig)
    {
        return buDeviceConfigMapper.selectBuDeviceConfigList(buDeviceConfig);
    }

    /**
     * 新增设备配置
     * 
     * @param buDeviceConfig 设备配置
     * @return 结果
     */
    @Override
    public int insertBuDeviceConfig(BuDeviceConfig buDeviceConfig)
    {
        buDeviceConfig.setCreateTime(DateUtils.getNowDate());
        return buDeviceConfigMapper.insertBuDeviceConfig(buDeviceConfig);
    }

    /**
     * 修改设备配置
     * 
     * @param buDeviceConfig 设备配置
     * @return 结果
     */
    @Override
    public int updateBuDeviceConfig(BuDeviceConfig buDeviceConfig)
    {
        buDeviceConfig.setUpdateTime(DateUtils.getNowDate());
        return buDeviceConfigMapper.updateBuDeviceConfig(buDeviceConfig);
    }

    /**
     * 批量删除设备配置
     * 
     * @param ids 需要删除的设备配置主键
     * @return 结果
     */
    @Override
    public int deleteBuDeviceConfigByIds(Long[] ids)
    {
        return buDeviceConfigMapper.deleteBuDeviceConfigByIds(ids);
    }

    /**
     * 删除设备配置信息
     * 
     * @param id 设备配置主键
     * @return 结果
     */
    @Override
    public int deleteBuDeviceConfigById(Long id)
    {
        return buDeviceConfigMapper.deleteBuDeviceConfigById(id);
    }
}
