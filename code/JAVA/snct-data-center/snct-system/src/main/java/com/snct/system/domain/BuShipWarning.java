package com.snct.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * 船舶预警日志对象 bu_ship_warning
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public class BuShipWarning extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 部门ID */
    private Long deptId;

    @Excel(name = "企业/部门")
    private String deptName;

    /** 船只sn号 */
    private String sn;

    @Excel(name = "船舶名称")
    private String shipName;
    /** 设备ID */
    private Long deviceId;
    @Excel(name = "设备名称")
    private String deviceName;

    /** hbase  数据表#数据key */
    private String dataKey;

    /** 预警标题 */
    @Excel(name = "预警信息")
    private String name;

    /** 预警类型 1台风预警  2姿态预警 3气象预警 4gps 5pdu  5猫  7功放 */
    //@Excel(name = "预警类型 1台风预警  2姿态预警 3气象预警 4gps 5pdu  5猫  7功放")
    private Long type;

    /** 预警级别  1一级预警  2 二级预警  3三级预警 */
    //@Excel(name = "预警级别  1一级预警  2 二级预警  3三级预警")
    private Long level;

    /** 状态0默认  1已处理 */
    //@Excel(name = "状态0默认  1已处理")
    private Long status;

    private String stime;
    private String etime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public void setSn(String sn) 
    {
        this.sn = sn;
    }

    public String getSn() 
    {
        return sn;
    }

    public String getDeptName()
    {
        return deptName;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }

    public void setShipName(String shipName)
    {
        this.shipName = shipName;
    }

    public String getShipName()
    {
        return shipName;
    }

    public void setDeviceId(Long deviceId) 
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() 
    {
        return deviceId;
    }

    public void setDeviceName(String deviceName)
    {
        this.deviceName = deviceName;
    }

    public String getDeviceName()
    {
        return deviceName;
    }

    public void setDataKey(String dataKey) 
    {
        this.dataKey = dataKey;
    }

    public String getDataKey() 
    {
        return dataKey;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setType(Long type) 
    {
        this.type = type;
    }

    public Long getType() 
    {
        return type;
    }

    public void setLevel(Long level) 
    {
        this.level = level;
    }

    public Long getLevel() 
    {
        return level;
    }

    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    public void setStime(String stime)
    {
        this.stime = stime;
    }

    public String getStime()
    {
        return stime;
    }

    public void setEtime(String etime)
    {
        this.etime = etime;
    }

    public String getEtime()
    {
        return etime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("sn", getSn())
            .append("deviceId", getDeviceId())
            .append("dataKey", getDataKey())
            .append("name", getName())
            .append("type", getType())
            .append("level", getLevel())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
