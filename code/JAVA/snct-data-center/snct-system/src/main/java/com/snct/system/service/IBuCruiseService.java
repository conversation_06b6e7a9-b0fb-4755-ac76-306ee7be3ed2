package com.snct.system.service;

import java.util.List;
import com.snct.system.domain.BuCruise;

/**
 * 航次Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface IBuCruiseService 
{
    /**
     * 查询航次
     * 
     * @param cruiseId 航次主键
     * @return 航次
     */
    public BuCruise selectBuCruiseByCruiseId(Long cruiseId);

    /**
     * 查询航次列表
     * 
     * @param buCruise 航次
     * @return 航次集合
     */
    public List<BuCruise> selectBuCruiseList(BuCruise buCruise);

    /**
     * 新增航次
     * 
     * @param buCruise 航次
     * @return 结果
     */
    public int insertBuCruise(BuCruise buCruise);

    /**
     * 修改航次
     * 
     * @param buCruise 航次
     * @return 结果
     */
    public int updateBuCruise(BuCruise buCruise);

    /**
     * 批量删除航次
     * 
     * @param cruiseIds 需要删除的航次主键集合
     * @return 结果
     */
    public int deleteBuCruiseByCruiseIds(Long[] cruiseIds);

    /**
     * 删除航次信息
     * 
     * @param cruiseId 航次主键
     * @return 结果
     */
    public int deleteBuCruiseByCruiseId(Long cruiseId);
}
